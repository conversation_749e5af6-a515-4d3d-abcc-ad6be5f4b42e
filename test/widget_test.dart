// This is a basic Flutter widget test for the Compass app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:compass_vi/main.dart';

void main() {
  testWidgets('Compass app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the home screen loads with the expected title
    expect(find.text('Chọn La <PERSON>à<PERSON>'), findsOneWidget);

    // Verify that both compass options are present
    expect(find.text('La bàn cơ bản'), findsOneWidget);
    expect(find.text('La bàn theo tuổi'), findsOneWidget);

    // Verify that user info section is present
    expect(find.text('Thông tin cá nhân'), findsOneWidget);
    expect(find.text('Giới tính:'), findsOneWidget);
    expect(find.text('Năm sinh:'), findsOneWidget);
  });

  testWidgets('Basic compass navigation test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Tap on the basic compass option
    await tester.tap(find.text('La bàn cơ bản'));
    await tester.pumpAndSettle();

    // Verify that we navigated to the compass screen
    expect(find.text('La Bàn Cơ Bản'), findsOneWidget);
  });
}
