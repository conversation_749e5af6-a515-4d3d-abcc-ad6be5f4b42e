import 'package:flutter/services.dart';

class FacebookAnalyticsService {
  static const MethodChannel _channel = MethodChannel('facebook_analytics');

  // Log app install event
  static Future<void> logAppInstall() async {
    try {
      await _channel.invokeMethod('logEvent', {
        'eventName': 'app_install',
        'parameters': {
          'platform': 'flutter',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }
      });
    } catch (e) {
      print('Error logging app install: $e');
    }
  }

  // Log screen view
  static Future<void> logScreenView(String screenName) async {
    try {
      await _channel.invokeMethod('logScreenView', {
        'screenName': screenName,
      });
    } catch (e) {
      print('Error logging screen view: $e');
    }
  }

  // Log compass usage
  static Future<void> logCompassUsage() async {
    try {
      await _channel.invokeMethod('logCompassUsage');
    } catch (e) {
      print('Error logging compass usage: $e');
    }
  }

  // Log feature usage
  static Future<void> logFeatureUsage(String feature) async {
    try {
      await _channel.invokeMethod('logEvent', {
        'eventName': 'feature_usage',
        'parameters': {
          'feature': feature,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }
      });
    } catch (e) {
      print('Error logging feature usage: $e');
    }
  }

  // Log user engagement
  static Future<void> logUserEngagement(String action,
      {Map<String, dynamic>? additionalParams}) async {
    try {
      final params = <String, dynamic>{
        'action': action,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      if (additionalParams != null) {
        params.addAll(additionalParams);
      }

      await _channel.invokeMethod('logEvent', {
        'eventName': 'user_engagement',
        'parameters': params,
      });
    } catch (e) {
      print('Error logging user engagement: $e');
    }
  }

  // Log custom event
  static Future<void> logEvent(
      String eventName, Map<String, dynamic> parameters) async {
    try {
      await _channel.invokeMethod('logEvent', {
        'eventName': eventName,
        'parameters': parameters,
      });
    } catch (e) {
      print('Error logging event: $e');
    }
  }

  // Log session duration
  static Future<void> logSessionDuration(int seconds) async {
    try {
      await _channel.invokeMethod('logEvent', {
        'eventName': 'session_duration',
        'parameters': {
          'duration_seconds': seconds,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }
      });
    } catch (e) {
      print('Error logging session duration: $e');
    }
  }
}
