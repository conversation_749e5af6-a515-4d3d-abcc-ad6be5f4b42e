import 'package:shared_preferences/shared_preferences.dart';

class StateManager {
  static final StateManager _instance = StateManager._internal();
  factory StateManager() => _instance;
  StateManager._internal();

  // Keys for SharedPreferences
  static const String _genderKey = 'user_gender';
  static const String _birthYearKey = 'user_birth_year';

  // User info
  String? _gender;
  int? _birthYear;

  // Getters
  String? get gender => _gender;
  int? get birthYear => _birthYear;
  
  bool get hasValidUserInfo => _gender != null && _birthYear != null;

  // Initialize from SharedPreferences
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _gender = prefs.getString(_genderKey);
    _birthYear = prefs.getInt(_birthYearKey);
  }

  // Set gender
  Future<void> setGender(String gender) async {
    _gender = gender;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_genderKey, gender);
  }

  // Set birth year
  Future<void> setBirthYear(int year) async {
    _birthYear = year;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_birthYearKey, year);
  }

  // Clear user info
  Future<void> clearUserInfo() async {
    _gender = null;
    _birthYear = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_genderKey);
    await prefs.remove(_birthYearKey);
  }

  // Get user age
  int? get userAge {
    if (_birthYear == null) return null;
    return DateTime.now().year - _birthYear!;
  }

  // Get compass direction based on age and gender (Vietnamese feng shui)
  String getCompassDirection() {
    if (!hasValidUserInfo) return '';
    
    final age = userAge!;
    final isMale = _gender == 'Nam';
    
    // Simplified Vietnamese feng shui compass direction calculation
    // This is based on traditional Vietnamese beliefs about lucky directions
    if (isMale) {
      switch (age % 9) {
        case 1: return 'Đông';
        case 2: return 'Đông Nam';
        case 3: return 'Nam';
        case 4: return 'Tây Nam';
        case 5: return 'Trung tâm';
        case 6: return 'Tây Bắc';
        case 7: return 'Tây';
        case 8: return 'Đông Bắc';
        case 0: return 'Bắc';
        default: return 'Không xác định';
      }
    } else {
      switch (age % 9) {
        case 1: return 'Tây Nam';
        case 2: return 'Tây';
        case 3: return 'Tây Bắc';
        case 4: return 'Đông';
        case 5: return 'Trung tâm';
        case 6: return 'Đông Nam';
        case 7: return 'Nam';
        case 8: return 'Bắc';
        case 0: return 'Đông Bắc';
        default: return 'Không xác định';
      }
    }
  }

  // Get lucky number based on age and gender
  int getLuckyNumber() {
    if (!hasValidUserInfo) return 0;
    
    final age = userAge!;
    final isMale = _gender == 'Nam';
    
    if (isMale) {
      return (11 - (age % 9)) % 9;
    } else {
      return (age % 9) + 4;
    }
  }
}
