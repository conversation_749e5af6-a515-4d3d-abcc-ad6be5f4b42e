import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class StreamBuilderCompassDegree extends StatefulWidget {
  const StreamBuilderCompassDegree({super.key});

  @override
  State<StreamBuilderCompassDegree> createState() => _StreamBuilderCompassDegreeState();
}

class _StreamBuilderCompassDegreeState extends State<StreamBuilderCompassDegree> {
  static const EventChannel _compassChannel = EventChannel('compass_stream');
  late final Stream<double> _compassStream;

  @override
  void initState() {
    super.initState();
    _compassStream = _compassChannel.receiveBroadcastStream().map((event) {
      if (event is double) return event;
      if (event is int) return event.toDouble();
      return 0.0;
    }).handleError((error) {
      debugPrint('Lỗi compass stream: $error');
      return 0.0;
    });
  }

  String _getDirectionText(double degree) {
    if (degree >= 337.5 || degree < 22.5) {
      return 'Bắc (N)';
    } else if (degree >= 22.5 && degree < 67.5) {
      return 'Đông Bắc (NE)';
    } else if (degree >= 67.5 && degree < 112.5) {
      return 'Đông (E)';
    } else if (degree >= 112.5 && degree < 157.5) {
      return 'Đông Nam (SE)';
    } else if (degree >= 157.5 && degree < 202.5) {
      return 'Nam (S)';
    } else if (degree >= 202.5 && degree < 247.5) {
      return 'Tây Nam (SW)';
    } else if (degree >= 247.5 && degree < 292.5) {
      return 'Tây (W)';
    } else if (degree >= 292.5 && degree < 337.5) {
      return 'Tây Bắc (NW)';
    }
    return 'Không xác định';
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<double>(
      stream: _compassStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Column(
            children: [
              Text(
                '---°',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'Đang tải...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
            ],
          );
        }

        if (snapshot.hasError) {
          return const Column(
            children: [
              Text(
                'Lỗi',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              Text(
                'Không thể đọc cảm biến',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
            ],
          );
        }

        final double degree = snapshot.data ?? 0.0;
        final String direction = _getDirectionText(degree);

        return Column(
          children: [
            Text(
              '${degree.toStringAsFixed(1)}°',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              direction,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
          ],
        );
      },
    );
  }
}
