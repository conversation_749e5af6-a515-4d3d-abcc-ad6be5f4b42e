import 'package:compass_vi/core/main_compass.dart';
import 'package:compass_vi/core/state_manager.dart';
import 'package:compass_vi/core/streambuilder_degree.dart';
import 'package:compass_vi/services/facebook_analytics_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class BatTrachScreen extends StatefulWidget {
  const BatTrachScreen({super.key});

  @override
  State<BatTrachScreen> createState() => _BatTrachScreenState();
}

class _BatTrachScreenState extends State<BatTrachScreen> {
  final StateManager _stateManager = StateManager();
  DateTime? _sessionStartTime;
  String _backgroundImage = 'assets/images_compass/background_1.jpg';
  String _compassImage = 'assets/images_compass/lb_e1.png';

  @override
  void initState() {
    super.initState();
    _sessionStartTime = DateTime.now();
    _loadUserCompassStyle();
    
    // Track screen view
    FacebookAnalyticsService.logScreenView('age_compass_screen');
    FacebookAnalyticsService.logCompassUsage();
  }

  Future<void> _loadUserCompassStyle() async {
    await _stateManager.initialize();
    
    if (_stateManager.hasValidUserInfo) {
      final age = _stateManager.userAge!;
      final isMale = _stateManager.gender == 'Nam';
      
      // Select background and compass based on age and gender
      final styleIndex = (age % 9) + 1;
      
      setState(() {
        _backgroundImage = 'assets/images_compass/background_$styleIndex.jpg';
        
        if (isMale) {
          _compassImage = 'assets/images_compass/lb_e$styleIndex.png';
        } else {
          _compassImage = 'assets/images_compass/lb_w${styleIndex > 4 ? styleIndex - 4 : styleIndex + 4}.png';
        }
      });
    }
  }

  @override
  void dispose() {
    // Track session duration
    if (_sessionStartTime != null) {
      final duration = DateTime.now().difference(_sessionStartTime!);
      FacebookAnalyticsService.logSessionDuration(duration.inSeconds);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xF6FFFFFF),
          ),
          onPressed: () {
            SystemChrome.setPreferredOrientations([]);
            Navigator.pop(context);
          },
        ),
        title: const Text(
          'La Bàn Theo Tuổi',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        backgroundColor: const Color(0xAEBE0A0A),
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Image.asset(
            _backgroundImage,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.black,
                child: const Center(
                  child: Text(
                    'Không thể tải hình nền',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              );
            },
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  // User info display
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Giới tính: ${_stateManager.gender ?? "Chưa xác định"}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'Tuổi: ${_stateManager.userAge ?? "Chưa xác định"}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'Hướng may mắn: ${_stateManager.getCompassDirection()}',
                          style: const TextStyle(
                            color: Colors.yellow,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 40),
                  Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            Image.asset(
                              'assets/images/khung_8.png',
                              height: 350,
                              width: 350,
                              fit: BoxFit.contain,
                            ),
                            CompassWidget(
                              compassImagePath: _compassImage,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const StreamBuilderCompassDegree(),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'La bàn được tùy chỉnh theo tuổi và giới tính của bạn\ntheo phong thủy truyền thống Việt Nam',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
