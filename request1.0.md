# Hướng Dẫn cho AI Agent: Tự động hoàn thiện ứng dụng theo mẫu và file assets có trước

## 1. Mục tiêu hoàn thành
Ứng dụng La Bàn Đại Việt. trong file hướng dẫn này sẽ có các bước hướng dẫn để AI Agent tự động hoàn thiện ứng dụng theo mẫu và reponsitory trên github. Mục đích AI Agent tự động đọc link mẫu trên github và tự động hoàn thiện ứng dụng theo mẫu repository đó. Cuối cùng để sản phẩm hoàn thiện là app có các chức năng, tính năng và giao diện giống như srouce code mẫu repository trên github.

## 2. link repository source code mẫu
https://github.com/MinhPro999/compass_vn.git
 - AI Agent đọ<PERSON> kỹ toàn bộ các file và thư mục trong repository mẫu này, lưu ý đọc và ghi nhớ kỹ code trong các thư mục android, ios, lib để flow theo và code giống như vậy cho dự án này.

### 3. Các yêu cầu chính
- thư mục assets chứa các hình ảnh đã được cung cấp sẵn tại dự án. cần cấu hình thư mục assets trong file pubspec.yaml 
- Cấu hình tên project các file liên quan thành 